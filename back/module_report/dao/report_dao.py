from datetime import datetime
from sqlalchemy import and_, delete, desc, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from module_admin.entity.do.user_do import SysUser
from module_report.entity.do.report_do import Report
from module_report.entity.vo.report_vo import ReportPageQueryModel, ReportQueryModel
from utils.page_util import PageUtil


class ReportDao:
    """
    周报月报数据库操作层
    """

    @classmethod
    async def get_report_list(cls, db: AsyncSession, query_object: ReportQueryModel):
        """
        根据查询参数获取周报月报列表

        :param db: orm对象
        :param query_object: 查询参数对象
        :return: 周报月报列表
        """
        query = select(Report, SysUser).outerjoin(
            SysUser, Report.reporter_id == SysUser.user_id
        ).where(Report.del_flag == '0')
        
        if query_object.report_type:
            query = query.where(Report.report_type == query_object.report_type)
        if query_object.reporter_id:
            query = query.where(Report.reporter_id == query_object.reporter_id)
        if query_object.report_date_start:
            query = query.where(Report.report_date >= query_object.report_date_start)
        if query_object.report_date_end:
            query = query.where(Report.report_date <= query_object.report_date_end)
        if query_object.is_saturated:
            query = query.where(Report.is_saturated == query_object.is_saturated)
        
        query = query.order_by(desc(Report.report_date), desc(Report.create_time))
        result = await db.execute(query)
        return result.all()

    @classmethod
    async def get_report_page(cls, db: AsyncSession, query_object: ReportPageQueryModel):
        """
        根据查询参数分页获取周报月报列表

        :param db: orm对象
        :param query_object: 查询参数对象
        :return: 周报月报分页列表
        """
        query = select(Report, SysUser).outerjoin(
            SysUser, Report.reporter_id == SysUser.user_id
        ).where(Report.del_flag == '0')
        
        if query_object.report_type:
            query = query.where(Report.report_type == query_object.report_type)
        if query_object.reporter_id:
            query = query.where(Report.reporter_id == query_object.reporter_id)
        if query_object.report_date_start:
            query = query.where(Report.report_date >= query_object.report_date_start)
        if query_object.report_date_end:
            query = query.where(Report.report_date <= query_object.report_date_end)
        if query_object.is_saturated:
            query = query.where(Report.is_saturated == query_object.is_saturated)
        
        query = query.order_by(desc(Report.report_date), desc(Report.create_time))
        return await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size)

    @classmethod
    async def get_report_by_id(cls, db: AsyncSession, report_id: int):
        """
        根据报告ID获取周报月报详情

        :param db: orm对象
        :param report_id: 报告ID
        :return: 周报月报详情
        """
        query = select(Report).where(Report.report_id == report_id, Report.del_flag == '0')
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_report_by_date_and_reporter(cls, db: AsyncSession, report_date: datetime, reporter_id: int, report_type: str):
        """
        根据报告日期、报告人和类型获取周报月报

        :param db: orm对象
        :param report_date: 报告日期
        :param reporter_id: 报告人ID
        :param report_type: 报告类型
        :return: 周报月报详情
        """
        query = select(Report).where(
            Report.report_date == report_date,
            Report.reporter_id == reporter_id,
            Report.report_type == report_type,
            Report.del_flag == '0'
        )
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def add_report(cls, db: AsyncSession, report: Report):
        """
        新增周报月报

        :param db: orm对象
        :param report: 周报月报对象
        :return: 新增的周报月报对象
        """
        db.add(report)
        await db.flush()
        return report

    @classmethod
    async def update_report(cls, db: AsyncSession, report: Report):
        """
        更新周报月报

        :param db: orm对象
        :param report: 周报月报对象
        :return: 更新结果
        """
        query = update(Report).where(Report.report_id == report.report_id).values(
            report_type=report.report_type,
            report_date=report.report_date,
            summary=report.summary,
            plan=report.plan,
            problems=report.problems,
            support_needed=report.support_needed,
            is_saturated=report.is_saturated,
            status=report.status,
            update_by=report.update_by,
            update_time=report.update_time,
            remark=report.remark
        )
        result = await db.execute(query)
        return result.rowcount

    @classmethod
    async def delete_report(cls, db: AsyncSession, report_ids: List[int]):
        """
        批量删除周报月报

        :param db: orm对象
        :param report_ids: 需要删除的周报月报ID列表
        :return: 删除结果
        """
        query = update(Report).where(Report.report_id.in_(report_ids)).values(del_flag='2')
        result = await db.execute(query)
        return result.rowcount
